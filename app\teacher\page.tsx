"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Users, Award, Plus, Star, LogOut, CheckCircle, Target } from "lucide-react"

// Mock data
const mockStudents = [
  {
    id: 1,
    name: "<PERSON><PERSON> <PERSON>",
    xp: 1250,
    level: 5,
    attendance: 95,
    badges: ["Good Student", "Perfect Attendance", "Helper"],
    recentActivity: "Completed math homework",
    goals: ["Improve handwriting", "Learn multiplication tables"],
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    xp: 980,
    level: 4,
    attendance: 88,
    badges: ["Team Player", "Creative Thinker"],
    recentActivity: "Helped classmate with reading",
    goals: ["Read 5 books this month", "Improve attendance"],
  },
  {
    id: 3,
    name: "Anita Singh",
    xp: 1100,
    level: 4,
    attendance: 92,
    badges: ["Quick Learner", "Good Behavior"],
    recentActivity: "Scored 90% in science test",
    goals: ["Master fractions", "Join art club"],
  },
]

const behaviorCategories = [
  { id: "attendance", name: "Attendance", xp: 10 },
  { id: "homework", name: "Homework Completion", xp: 25 },
  { id: "behavior", name: "Good Behavior", xp: 15 },
  { id: "participation", name: "Class Participation", xp: 20 },
  { id: "hygiene", name: "Personal Hygiene", xp: 10 },
  { id: "helping", name: "Helping Others", xp: 30 },
]

export default function TeacherDashboard() {
  const [user, setUser] = useState<any>(null)
  const [selectedStudent, setSelectedStudent] = useState("")
  const [xpForm, setXpForm] = useState({ category: "", points: "", note: "" })
  const [goalForm, setGoalForm] = useState({ student: "", goal: "", deadline: "" })

  useEffect(() => {
    const userData = localStorage.getItem("user")
    if (userData) {
      const parsedUser = JSON.parse(userData)
      if (parsedUser.role !== "teacher") {
        alert("Access denied. Teacher role required.")
        window.location.href = "/"
        return
      }
      setUser(parsedUser)
    } else {
      window.location.href = "/"
    }
  }, [])

  const handleLogout = () => {
    localStorage.removeItem("user")
    window.location.href = "/"
  }

  const handleAwardXP = (e: React.FormEvent) => {
    e.preventDefault()
    alert(`Awarded ${xpForm.points} XP to student for ${xpForm.category}`)
    setXpForm({ category: "", points: "", note: "" })
  }

  const handleSetGoal = (e: React.FormEvent) => {
    e.preventDefault()
    alert(`Goal set for student: ${goalForm.goal}`)
    setGoalForm({ student: "", goal: "", deadline: "" })
  }

  if (!user) {
    return <div>Loading...</div>
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Teacher Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user.name}</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="secondary">Teacher</Badge>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">My Students</p>
                  <p className="text-3xl font-bold">{mockStudents.length}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">XP Awarded Today</p>
                  <p className="text-3xl font-bold">450</p>
                </div>
                <Award className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Attendance</p>
                  <p className="text-3xl font-bold">92%</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Goals</p>
                  <p className="text-3xl font-bold">12</p>
                </div>
                <Target className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="students" className="space-y-6">
          <TabsList>
            <TabsTrigger value="students">My Students</TabsTrigger>
            <TabsTrigger value="evaluate">Behavior Evaluation</TabsTrigger>
            <TabsTrigger value="goals">Set Goals</TabsTrigger>
            <TabsTrigger value="badges">Award Badges</TabsTrigger>
          </TabsList>

          <TabsContent value="students">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {mockStudents.map((student) => (
                <Card key={student.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">{student.name}</CardTitle>
                      <Badge variant="outline">Level {student.level}</Badge>
                    </div>
                    <CardDescription>
                      {student.xp} XP • {student.attendance}% attendance
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="text-sm font-medium">Progress to Next Level</span>
                        <span className="text-sm text-gray-600">{student.xp}/1500 XP</span>
                      </div>
                      <Progress value={(student.xp / 1500) * 100} className="h-2" />
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Recent Activity</p>
                      <p className="text-sm text-gray-600">{student.recentActivity}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Badges ({student.badges.length})</p>
                      <div className="flex flex-wrap gap-1">
                        {student.badges.map((badge, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {badge}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <p className="text-sm font-medium mb-2">Current Goals</p>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {student.goals.map((goal, index) => (
                          <li key={index} className="flex items-center gap-2">
                            <Target className="h-3 w-3" />
                            {goal}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="evaluate">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Award XP Points</CardTitle>
                  <CardDescription>Recognize student achievements and behaviors</CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleAwardXP} className="space-y-4">
                    <div>
                      <Label htmlFor="student-select">Select Student</Label>
                      <Select value={selectedStudent} onValueChange={setSelectedStudent}>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a student" />
                        </SelectTrigger>
                        <SelectContent>
                          {mockStudents.map((student) => (
                            <SelectItem key={student.id} value={student.id.toString()}>
                              {student.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="category">Behavior Category</Label>
                      <Select
                        value={xpForm.category}
                        onValueChange={(value) => setXpForm({ ...xpForm, category: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {behaviorCategories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name} (+{category.xp} XP)
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="points">XP Points</Label>
                      <Input
                        id="points"
                        type="number"
                        value={xpForm.points}
                        onChange={(e) => setXpForm({ ...xpForm, points: e.target.value })}
                        placeholder="Enter XP amount"
                      />
                    </div>

                    <div>
                      <Label htmlFor="note">Note (Optional)</Label>
                      <Textarea
                        id="note"
                        value={xpForm.note}
                        onChange={(e) => setXpForm({ ...xpForm, note: e.target.value })}
                        placeholder="Add a motivational note..."
                        rows={3}
                      />
                    </div>

                    <Button type="submit" className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Award XP
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Behavior Categories</CardTitle>
                  <CardDescription>Quick reference for XP values</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {behaviorCategories.map((category) => (
                      <div key={category.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <span className="font-medium">{category.name}</span>
                        <Badge variant="outline">+{category.xp} XP</Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="goals">
            <Card>
              <CardHeader>
                <CardTitle>Set Personal Goals</CardTitle>
                <CardDescription>Create personalized goals for students</CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSetGoal} className="space-y-4 max-w-md">
                  <div>
                    <Label htmlFor="goal-student">Select Student</Label>
                    <Select
                      value={goalForm.student}
                      onValueChange={(value) => setGoalForm({ ...goalForm, student: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a student" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockStudents.map((student) => (
                          <SelectItem key={student.id} value={student.id.toString()}>
                            {student.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="goal-text">Goal Description</Label>
                    <Textarea
                      id="goal-text"
                      value={goalForm.goal}
                      onChange={(e) => setGoalForm({ ...goalForm, goal: e.target.value })}
                      placeholder="Describe the goal..."
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="deadline">Target Date</Label>
                    <Input
                      id="deadline"
                      type="date"
                      value={goalForm.deadline}
                      onChange={(e) => setGoalForm({ ...goalForm, deadline: e.target.value })}
                    />
                  </div>

                  <Button type="submit" className="w-full">
                    <Target className="h-4 w-4 mr-2" />
                    Set Goal
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="badges">
            <Card>
              <CardHeader>
                <CardTitle>Award Special Badges</CardTitle>
                <CardDescription>Recognize exceptional achievements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    {
                      name: "Perfect Attendance",
                      description: "100% attendance for a month",
                      color: "bg-green-100 text-green-800",
                    },
                    {
                      name: "Academic Excellence",
                      description: "Top performer in academics",
                      color: "bg-blue-100 text-blue-800",
                    },
                    {
                      name: "Team Leader",
                      description: "Outstanding leadership skills",
                      color: "bg-purple-100 text-purple-800",
                    },
                    {
                      name: "Creative Genius",
                      description: "Exceptional creativity",
                      color: "bg-pink-100 text-pink-800",
                    },
                    { name: "Helper", description: "Always helps classmates", color: "bg-yellow-100 text-yellow-800" },
                    {
                      name: "Improvement Star",
                      description: "Most improved student",
                      color: "bg-orange-100 text-orange-800",
                    },
                  ].map((badge, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-3">
                      <div
                        className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${badge.color}`}
                      >
                        <Star className="h-4 w-4 mr-1" />
                        {badge.name}
                      </div>
                      <p className="text-sm text-gray-600">{badge.description}</p>
                      <Button size="sm" variant="outline" className="w-full bg-transparent">
                        Award Badge
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
