# ImpactScore - NGO Student Growth Tracker

A gamified learning platform designed for NGO student progress tracking with multi-role access and comprehensive analytics.

## 🌟 Features

- **Multi-Role Access**: Admin, Teacher/Volunteer, Student, and Parent dashboards
- **Gamification**: XP Points, Badges, and Leaderboards to motivate learning
- **Progress Tracking**: Monitor behavior, academics, and attendance
- **Impact Analytics**: Comprehensive reports, trends, and insights
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## 🚀 Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Radix UI Components
- **Icons**: Lucide React
- **Charts**: Recharts
- **Forms**: React Hook Form with Zod validation

## 🏃‍♂️ Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/Ayush20Thakur/impactscore-ngo-tracker.git
cd impactscore-ngo-tracker
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🔐 Demo Credentials

- **Admin**: <EMAIL> / password
- **Teacher**: <EMAIL> / password
- **Student**: <EMAIL> / password
- **Parent**: <EMAIL> / password

## 📱 User Roles

### Admin Dashboard
- Manage all users and centers
- View comprehensive analytics
- System configuration
- Generate reports

### Teacher/Volunteer Dashboard
- Manage assigned students
- Track daily activities
- Award points and badges
- View class analytics

### Student Dashboard
- View personal progress
- Check XP points and badges
- Access learning materials
- Track attendance

### Parent Dashboard
- Monitor child's progress
- View academic performance
- Communication with teachers
- Attendance tracking

## 🎯 Key Components

- **Gamification System**: Points, badges, and leaderboards
- **Progress Tracking**: Academic, behavioral, and attendance metrics
- **Analytics Dashboard**: Visual charts and progress reports
- **Multi-Center Support**: Manage multiple NGO locations
- **Responsive UI**: Mobile-first design approach

## 🛠️ Development

### Build for Production

```bash
npm run build
```

### Lint Code

```bash
npm run lint
```

## 🚀 Deployment

This project is optimized for deployment on Vercel with the following settings:

### Vercel Configuration:
- **Build Command**: `npm run build`
- **Output Directory**: `.next`
- **Install Command**: `npm install`
- **Node.js Version**: 18.x

### Deploy Steps:
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Use the configuration above
4. Deploy!

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/Ayush20Thakur/impactscore-ngo-tracker)

### Recent Fixes:
- ✅ Downgraded to React 18 for better compatibility
- ✅ Updated to Next.js 14 for stability
- ✅ Removed conflicting dependencies
- ✅ Fixed build errors for Vercel deployment

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support and questions, please open an issue on GitHub.

---

Built with ❤️ for NGO communities to track and celebrate student growth.
